{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/user-admin-api/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "apps/user-admin-api/tsconfig.app.json"}, "monorepo": true, "root": "apps/user-admin-api", "projects": {"user-admin-api": {"type": "application", "root": "apps/user-admin-api", "entryFile": "main", "sourceRoot": "apps/user-admin-api/src", "compilerOptions": {"tsConfigPath": "apps/user-admin-api/tsconfig.app.json"}}, "users": {"type": "application", "root": "apps/users", "entryFile": "main", "sourceRoot": "apps/users/src", "compilerOptions": {"tsConfigPath": "apps/users/tsconfig.app.json"}}}}