// import { Controller, Get, Post, Put, Delete, Param, Body } from '@nestjs/common';
// import { UsersService } from './users.service';
// import { User } from './users.entity';

// @Controller('users')
// export class UsersController {
//   constructor(private readonly usersService: UsersService) {}

//   // GET /users
//   @Get()
//   findAll(): Promise<User[]> {
//     return this.usersService.findAll();
//   }

//   // GET /users/:id
//   @Get(':id')
//   findOne(@Param('id') id: string): Promise<User | null> {
//     return this.usersService.findOne(Number(id));
//   }

//   // POST /users
//   @Post()
//   create(@Body() userData: Partial<User>): Promise<User> {
//     return this.usersService.create(userData);
//   }

//   // PUT /users/:id
//   @Put(':id')
//   update(@Param('id') id: string, @Body() userData: Partial<User>): Promise<User | null> {
//     return this.usersService.update(Number(id), userData);
//   }

//   // DELETE /users/:id
//   @Delete(':id')
//   remove(@Param('id') id: string): Promise<void> {
//     return this.usersService.remove(Number(id));
//   }
// }


import { Controller, Get, Post, Put, Delete, Param, Body } from '@nestjs/common';
import { UsersService } from './users.service';
import { User } from './users.entity';

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  // GET /users
  @Get()
  findAll(): Promise<User[]> {
    return this.usersService.findAll();
  }

  // GET /users/:id
  @Get(':id')
  findOne(@Param('id') id: string): Promise<User> {
    return this.usersService.findOne(Number(id));
  }

  // POST /users
  @Post()
  create(@Body() userData: Partial<User>): Promise<User> {
    return this.usersService.create(userData);
  }

  // PUT /users/:id
  @Put(':id')
  update(@Param('id') id: string, @Body() userData: Partial<User>): Promise<User> {
    return this.usersService.update(Number(id), userData);
  }

  // DELETE /users/:id
  @Delete(':id')
  remove(@Param('id') id: string): Promise<void> {
    return this.usersService.remove(Number(id));
  }
}
