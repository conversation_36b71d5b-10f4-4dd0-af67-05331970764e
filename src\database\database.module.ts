import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../users/users.entity';

@Module({
  imports: [
    TypeOrmModule.forRoot({
      type: 'mysql',
      host: 'localhost',       // MySQL host
      port: 3306,              // MySQL port
      username: 'root',        // 🔹 your MySQL username
      password: 'password',    // 🔹 your MySQL password
      database: 'dashboardusers', // 🔹 your database
      entities: [User],        // link entity -> table
      synchronize: true,       // auto-create/modify table (dev only)
    }),
  ],
})
export class DatabaseModule {}

