import { Injectable } from '@nestjs/common';

@Injectable()
export class UsersService {
  private users = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>' },
    { id: 2, name: '<PERSON>', email: '<EMAIL>' },
  ];

  findAll() {
    return this.users;
  }

  findOne(id: number) {
    return this.users.find(user => user.id === id);
  }

  create(user: { name: string; email: string }) {
    const newUser = {
      id: Date.now(),
      ...user,
    };
    this.users.push(newUser);
    return newUser;
  }

  update(id: number, updatedUser: { name?: string; email?: string }) {
    const user = this.findOne(id);
    if (!user) return null;
    Object.assign(user, updatedUser);
    return user;
  }

  remove(id: number) {
    this.users = this.users.filter(user => user.id !== id);
    return { deleted: true };
  }
}

