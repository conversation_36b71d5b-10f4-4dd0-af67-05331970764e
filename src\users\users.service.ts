// import { Injectable } from '@nestjs/common';
// import { InjectRepository } from '@nestjs/typeorm';
// import { Repository } from 'typeorm';
// import { User } from './users.entity';

// @Injectable()
// export class UsersService {
//   constructor(
//     @InjectRepository(User)
//     private readonly userRepository: Repository<User>, // 👈 TypeORM repo
//   ) {}

//   // Get all users
//   findAll(): Promise<User[]> {
//     return this.userRepository.find();
//   }

//   // Get one user by ID
//   findOne(id: number): Promise<User | null> {
//     return this.userRepository.findOneBy({ id });
//   }

//   // Create a new user
//   create(user: Partial<User>): Promise<User> {
//     const newUser = this.userRepository.create(user); // 👈 prepare entity
//     return this.userRepository.save(newUser);        // 👈 insert into DB
//   }

//   // Update user by ID
//   async update(id: number, userData: Partial<User>): Promise<User | null> {
//     await this.userRepository.update(id, userData);
//     return this.userRepository.findOneBy({ id }); // return updated record
//   }

//   // Delete user by ID
//   async remove(id: number): Promise<void> {
//     await this.userRepository.delete(id);
//   }
// }


import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { User } from './users.entity';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  // Get all users
  findAll(): Promise<User[]> {
    return this.userRepository.find();
  }

  // Get one user by ID
  async findOne(id: number): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    return user;
  }

  // Create a new user (with password hashing)
  async create(userData: Partial<User>): Promise<User> {
    if (userData.password) {
      const salt = await bcrypt.genSalt();
      userData.password = await bcrypt.hash(userData.password, salt);
    }

    const newUser = this.userRepository.create(userData);
    return this.userRepository.save(newUser);
  }

  // Update user by ID (rehash password if updated)
  async update(id: number, userData: Partial<User>): Promise<User> {
    const existingUser = await this.findOne(id);

    if (userData.password) {
      const salt = await bcrypt.genSalt();
      userData.password = await bcrypt.hash(userData.password, salt);
    }

    Object.assign(existingUser, userData);
    return this.userRepository.save(existingUser);
  }

  // Delete user by ID
  async remove(id: number): Promise<void> {
    const result = await this.userRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
  }
}
