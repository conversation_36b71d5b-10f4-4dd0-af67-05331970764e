Stack trace:
Frame         Function      Args
0007FFFF3440  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFF3440, 0007FFFF2340) msys-2.0.dll+0x1FEBA
0007FFFF3440  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF3718) msys-2.0.dll+0x67F9
0007FFFF3440  000210046832 (000210285FF9, 0007FFFF32F8, 0007FFFF3440, 000000000000) msys-2.0.dll+0x6832
0007FFFF3440  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF3440  0002100690B4 (0007FFFF3450, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF3720  00021006A49D (0007FFFF3450, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA88010000 ntdll.dll
7FFA87670000 KERNEL32.DLL
7FFA859B0000 KERNELBASE.dll
7FFA87B80000 USER32.dll
7FFA85CB0000 win32u.dll
7FFA86B00000 GDI32.dll
7FFA85790000 gdi32full.dll
000210040000 msys-2.0.dll
7FFA856C0000 msvcp_win.dll
7FFA858B0000 ucrtbase.dll
7FFA86900000 advapi32.dll
7FFA86B30000 msvcrt.dll
7FFA86020000 sechost.dll
7FFA869C0000 RPCRT4.dll
7FFA85760000 bcrypt.dll
7FFA85010000 CRYPTBASE.DLL
7FFA85F70000 bcryptPrimitives.dll
7FFA87E50000 IMM32.DLL
